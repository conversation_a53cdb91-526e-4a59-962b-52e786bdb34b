import java.util.ArrayList;

class MyThreadPool {
    private int size;
    private ArrayList<WorkingThread> threads = new ArrayList<>();
    private ArrayList<Runnable> taskQueue = new ArrayList<>();

    public MyThreadPool() {
        this(3);
    }

    public MyThreadPool(int s) {
        size = s;
        for (int i = 0; i < this.size; i++) {
            WorkingThread t = new WorkingThread(this);
            threads.add(t);
            t.start();
        }
    }

    public void add(Runnable task) {
        //Handle here
        //Them 1 task vao hang cho
        //Is this add below reasonable?
        this.taskQueue.add(task);
    }

    public void shutDown() {
        for (WorkingThread t : threads) {
            t.interrupt();
        }
        for (WorkingThread t : threads) {
            try {
                t.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}

class WorkingThread extends Thread {
    private MyThreadPool myThreadPool;

    public WorkingThread(MyThreadPool pool) {
        myThreadPool = pool;
    }

    @Override
    public void run() {
        // Handle more here
        // while //ngung khi co interupe
        // lay task ra khoi taskque neu co, neu khong co thi phai cho wait on condition
        // variable
        // chay task nay
        while (!Thread.currentThread().isInterrupted()) {

        }
    }
}