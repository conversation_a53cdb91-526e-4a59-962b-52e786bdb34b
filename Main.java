public class Main {
    public static void main(String[] args) throws InterruptedException {
        var pool = new MyThreadPool(5);
        for (int index = 0; index < 100; index++) {
            var t = new Task("" + index);
            pool.add(t);
        }

        Thread.sleep(10000);

        pool.shutDown();
        System.out.println("Done");
    }
}

class Task implements Runnable {
    private String name;

    public Task(String name) {
        this.name = name;
    }

    @Override
    public void run() {
        System.out.println("Task " + name + " is running");
    }
}